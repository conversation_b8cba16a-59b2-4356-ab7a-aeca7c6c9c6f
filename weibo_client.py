"""
微博客户端模块
"""
import requests
import json
import re
import time
from typing import Dict, List, Tuple
from bs4 import BeautifulSoup
from utils import get_random_user_agent, random_delay, safe_get, RetryHelper, get_checkin_btn


class WeiboClient:
    """微博客户端类"""
    
    def __init__(self, logger):
        self.logger = logger
        self.session = requests.Session()
        self.retry_helper = RetryHelper(max_retries=3, delay=1.0)
#        self.last_visited_url = "https://weibo.com"  # 记录最后访问的页面，用于设置Referer

        # 设置默认请求头
        self.session.headers.update({
#            'User-Agent': get_random_user_agent(),
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br, zstd'
        })
    
    def set_cookies(self, cookies: Dict) -> None:
        """设置cookies"""
        self.session.cookies.update(cookies)
        self.logger.info("已更新cookies")
    
    def get_cookies(self) -> Dict:
        """获取当前cookies"""
        return dict(self.session.cookies)
    """
    def _get_smart_referer(self, target_url: str) -> str:
        # 智能获取Referer
        # 根据目标URL智能选择合适的Referer
        if '/p/' in target_url and '/super_index' in target_url:
            # 访问超话页面，Referer设为超话列表页
            return "https://weibo.com/p/100808237347456f0169aa3c4843505d877bc2"
        elif '/ajax/' in target_url:
            # API请求，Referer设为相关页面
            return "https://weibo.com/mygroups"
        elif '/aj/general/button' in target_url:
            # 签到请求，Referer应该是超话页面（由调用方指定）
            return self.last_visited_url
        else:
            # 其他请求，使用最后访问的页面
            return self.last_visited_url
   
    def _update_last_visited_url(self, url: str) -> None:
        # 更新最后访问的URL
        # 只记录页面URL，不记录API URL
        if not ('/ajax/' in url or '/aj/' in url):
            self.last_visited_url = url
    """
    def login_by_cookies(self, cookies: Dict) -> bool:
        """通过cookies登录"""
        try:
            self.set_cookies(cookies)
            
            # 验证cookies是否有效
            response = self.session.get('https://weibo.com/')
            if response.status_code == 200 and 'login.sina.com.cn' not in response.url:
                self.logger.info("Cookies登录成功")
                return True
            else:
                self.logger.warning("Cookies已失效，需要重新登录")
                return False
                
        except Exception as e:
            self.logger.error(f"Cookies登录失败: {e}")
            return False
    
    def get_user_topics(self) -> List[Dict]:
        """获取用户关注的超话列表"""
        topics = []

        # 尝试多个不同的页面来获取超话
        urls_to_try = [
            "https://weibo.com/u/page/follow/3210115992/231093_-_chaohua"
        ]

        for url in urls_to_try:
            try:
                self.logger.info(f"尝试从 {url} 获取超话列表")
                response = self.session.get(url)

                if response.status_code != 200:
                    self.logger.warning(f"访问 {url} 失败，状态码: {response.status_code}")
                    continue

                # 检查是否被重定向到登录页面
                if 'login.sina.com.cn' in response.url:
                    self.logger.warning(f"访问 {url} 被重定向到登录页面")
                    continue

                soup = BeautifulSoup(response.text, 'html.parser')

                # 尝试多种方式查找超话链接
                patterns = [
                    r'/p/(\w+)/super_index',  # 新版超话链接格式
                    r'/p/(\d+)',              # 旧版超话链接格式
                    r'/p/(\w+)'               # 通用超话链接格式
                ]

                for pattern in patterns:
                    topic_links = soup.find_all('a', href=re.compile(pattern))

                    for link in topic_links:
                        href = link.get('href', '')
                        title = link.get_text(strip=True)

                        # 提取超话ID
                        match = re.search(pattern, href)
                        if match and title and len(title) > 1:
                            topic_id = match.group(1)

                            # 过滤掉一些无效的链接文本
                            if title not in ['超话', '更多', '查看更多', '展开', '收起']:
                                topic_info = {
                                    'id': topic_id,
                                    'name': title,
                                    'url': f"https://weibo.com{href}" if href.startswith('/') else href
                                }

                                # 避免重复添加
                                if not any(t['id'] == topic_id for t in topics):
                                    topics.append(topic_info)

                if topics:
                    self.logger.info(f"从 {url} 获取到 {len(topics)} 个超话")
                    break
                else:
                    self.logger.info(f"从 {url} 未获取到超话")

            except Exception as e:
                self.logger.error(f"从 {url} 获取超话列表异常: {e}")
                continue

        # 如果还是没有找到超话，尝试解析页面内容查找更多信息
        if not topics:
            self.logger.warning("未找到超话，尝试分析页面内容...")
            try:
                response = self.session.get("https://weibo.com/u/page/follow/3210115992/231093_-_chaohua")
                if response.status_code == 200:
                    # 保存页面内容用于调试
                    with open('debug_page.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    self.logger.info("页面内容已保存到 debug_page.html，可用于调试")
            except Exception as e:
                self.logger.error(f"保存调试页面失败: {e}")

        # 如果通过页面解析没有找到超话，尝试API方式
        if not topics:
            topics = self._try_api_methods()

        self.logger.info(f"总共获取到 {len(topics)} 个超话")
        return topics

    def _try_api_methods(self) -> List[Dict]:
        """尝试通过API方式获取超话"""
        topics = []

        # 尝试微博桌面端API
        api_urls = [
            "https://weibo.com/ajax/profile/topicContent?tabid=231093_-_chaohua"
        ]

        for api_url in api_urls:
            try:
                self.logger.info(f"尝试API: {api_url}")
                headers = {
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://weibo.com/u/page/follow/3210115992/231093_-_chaohua'
                }

                response = self.session.get(api_url, headers=headers)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        #self.logger.info(f"API {api_url} 返回数据: {str(data)}")

                        """
                        # 尝试解析不同的数据结构
                        if 'data' in data:
                            if isinstance(data['data'], list):
                                for item in data['data']:
                                    if isinstance(item, dict) and 'id' in item:
                                        topics.append({
                                            'id': str(item['id']),
                                            'name': item.get('name', item.get('title', '未知超话')),
                                            'url': f"https://weibo.com/p/{item['id']}/super_index"
                                        })
                        """

                        for item in data['data']['list']:
                            # 处理 oid：保留冒号后面的部分
                            oid_parts = item['oid'].split(':')
                            processed_oid = oid_parts[-1] if len(oid_parts) > 1 else item['oid']
                            
                            # 处理 link：添加 https 前缀
                            processed_link = 'https:' + item['link']+'/super_index' if not item['link'].startswith('https') else item['link']
                            
                            topics_items = {
                                'name': item['title'],
                                'id': str(processed_oid),
                                'url': processed_link
                            }
                            
                            topics.append(topics_items)
                        self.logger.info(f"API {api_url} 返回数据: {str(topics)}")

                        if topics:
                            self.logger.info(f"通过API {api_url} 获取到 {len(topics)} 个超话")
                            break

                    except json.JSONDecodeError:
                        self.logger.warning(f"API {api_url} 返回非JSON数据")

            except Exception as e:
                self.logger.error(f"API {api_url} 请求异常: {e}")

        return topics

    def check_topic_status(self, topic_id: str) -> Dict:
        """检查超话签到状态"""
        try:
            url = f"https://weibo.com/p/{topic_id}/super_index"
            """
            # 设置合适的Referer
            headers = {'Referer': self._get_smart_referer(url)}
            response = self.session.get(url, headers=headers)

            # 更新最后访问的URL
            if response.status_code == 200:
                self._update_last_visited_url(url)

            if response.status_code != 200:
                return {'success': False, 'message': f'访问超话页面失败: {response.status_code}'}

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找签到按钮或已签到标识
            checkin_btn = soup.find('a', {'action-type': 'checkinwidget_take'})
            if checkin_btn:
                return {'success': True, 'checked_in': False, 'message': '可以签到'}

            # 查找已签到标识
            checked_text = soup.find(text=re.compile(r'已签到|今日已签'))
            if checked_text:
                return {'success': True, 'checked_in': True, 'message': '今日已签到'}

            return {'success': False, 'message': '无法确定签到状态'}
            """
            # 设置合适的请求头
            headers = {'Referer': 'f"https://weibo.com/p/{topic_id}'}
            response = self.session.get(url, headers=headers)

            if response.status_code != 200:
                return {'success': False, 'message': f'访问超话页面失败: {response.status_code}'}

            #soup = BeautifulSoup(response.text, 'html.parser')

            # 查找签到按钮或已签到标识
            checkin_btn = get_checkin_btn(response.text)

            if checkin_btn:
                checked_text = checkin_btn.find('span').get_text().strip()
                print(checked_text)
                if  checked_text == '已签到':
                    return {'success': True, 'checked_in': True, 'message': '今日已签到'}
                else:
                    return {'success': True, 'checked_in': False, 'message': '可以签到'}

            return {'success': False, 'message': '无法确定签到状态'}    

        except Exception as e:
            self.logger.error(f"检查超话 {topic_id} 状态异常: {e}")
            return {'success': False, 'message': f'检查状态异常: {e}'}
    
    def checkin_topic(self, topic_id: str, topic_name: str = "") -> Dict:
        """签到超话"""
        try:
            # 首先检查签到状态
            status = self.check_topic_status(topic_id)
            if not status['success']:
                return status
            
            if status['checked_in']:
                return {'success': True, 'message': '今日已签到', 'already_checked': True}
            
            # 执行签到
            checkin_url = "https://weibo.com/p/aj/general/button"

            # 获取必要的参数
            topic_url = f"https://weibo.com/p/{topic_id}/super_index"
            print(topic_url)
            """
            # 访问超话页面时设置正确的Referer
            page_headers = {'Referer': self._get_smart_referer(topic_url)}
            response = self.session.get(topic_url, headers=page_headers)

            # 更新最后访问的URL
            if response.status_code == 200:
                self._update_last_visited_url(topic_url)

            if response.status_code != 200:
                return {'success': False, 'message': '获取签到参数失败'}

            # 从页面中提取必要参数
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找签到按钮获取参数
            checkin_btn = soup.find('a', {'action-type': 'checkin'})
            if not checkin_btn:
                return {'success': False, 'message': '未找到签到按钮'}
            """
            # 构造签到请求参数
            params = {
                'ajwvr': '6',
                'api': 'http://i.huati.weibo.com/aj/super/checkin',
                'texta': '签到',
                'textb': '已签到',
                'status': '0',
                'id': topic_id,
                'location': 'page_100808_super_index',
                'timezone': 'GMT+0800',
                'lang': 'zh-cn',
                'plat': 'Win32',
                'ua':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'screen': '1366*768',
                '__rnd': str(int(time.time() * 1000))
            }

            # 签到请求的Referer必须是超话页面
            headers = {
                'Referer': topic_url,  # 签到请求必须来自超话页面
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept':'*/*'
            }
            
            # 发送签到请求
            response = self.session.get(checkin_url, params=params, headers=headers)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(result)
                    if result.get('code') == '100000':
                        self.logger.info(f"超话 {topic_name}({topic_id}) 签到成功")
                        return {'success': True, 'message': '签到成功'}
                    else:
                        message = result.get('msg', '签到失败')
                        self.logger.warning(f"超话 {topic_name}({topic_id}) 签到失败: {message}")
                        return {'success': False, 'message': message}
                except json.JSONDecodeError:
                    self.logger.error(f"签到响应解析失败: {response.text}")
                    return {'success': False, 'message': '签到响应解析失败'}
            else:
                return {'success': False, 'message': f'签到请求失败: {response.status_code}'}
                
        except Exception as e:
            self.logger.error(f"签到超话 {topic_id} 异常: {e}")
            return {'success': False, 'message': f'签到异常: {e}'}
    
    def batch_checkin(self, topics: List[Dict], delay: float = 2.0) -> Dict:
        """批量签到超话"""
        results = {
            'total': len(topics),
            'success': 0,
            'failed': 0,
            'already_checked': 0,
            'details': []
        }
        
        for i, topic in enumerate(topics):
            topic_id = topic.get('id', '')
            topic_name = topic.get('name', '')
            
            self.logger.info(f"正在签到第 {i+1}/{len(topics)} 个超话: {topic_name}")
            
            # 执行签到
            result = self.retry_helper.retry(self.checkin_topic, topic_id, topic_name)
            
            # 统计结果
            if result['success']:
                if result.get('already_checked'):
                    results['already_checked'] += 1
                else:
                    results['success'] += 1
            else:
                results['failed'] += 1
            
            # 记录详细结果
            results['details'].append({
                'topic_id': topic_id,
                'topic_name': topic_name,
                'result': result
            })
            
            # 延迟避免请求过快
            if i < len(topics) - 1:
                random_delay(delay * 0.8, delay * 1.2)
        
        return results
