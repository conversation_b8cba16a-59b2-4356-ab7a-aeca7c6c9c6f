# 微博超话自动签到程序（多用户版）技术文档

## 📋 目录

1. [项目概述](#项目概述)
2. [功能介绍](#功能介绍)
3. [系统架构图](#系统架构图)
4. [技术栈介绍](#技术栈介绍)
5. [代码结构说明](#代码结构说明)
6. [核心算法解释](#核心算法解释)
7. [数据流程机制](#数据流程机制)
8. [安全机制原理](#安全机制原理)
9. [配置文件详解](#配置文件详解)
10. [完整部署教程](#完整部署教程)
11. [使用指南](#使用指南)
12. [故障排除指南](#故障排除指南)

---

## 📖 项目概述

### 什么是微博超话自动签到程序？

想象一下，您关注了很多微博超话（比如明星、游戏、兴趣爱好等话题），每天都需要手动点击签到来获取积分或保持活跃度。这个程序就像一个"智能助手"，可以自动帮您完成这些重复的签到操作。

**简单类比**：就像设置手机闹钟一样，您设置好时间和要签到的超话，程序就会在指定时间自动帮您完成签到，无需您亲自操作。

### 为什么需要多用户版本？

就像一个家庭可能有多个人都在使用微博一样，这个程序现在可以同时管理多个微博账户：
- 👨‍👩‍👧‍👦 **家庭共享**：爸爸、妈妈、孩子的账户都可以一起管理
- 🏢 **工作需要**：管理多个企业或个人账户
- 🎯 **效率提升**：一次操作，多个账户同时签到
- 💡 **智能管理**：每个账户独立配置，互不干扰

### 主要优势

- ✅ **自动化**：无需手动操作，程序自动完成签到
- ✅ **多账户**：同时管理多个微博账户
- ✅ **安全性**：加密存储账户信息，保护隐私
- ✅ **智能化**：自动发现关注的超话，智能重试失败操作
- ✅ **可定制**：灵活的配置选项，满足不同需求
- ✅ **并发处理**：支持同时为多个账户签到，提高效率
- ✅ **详细日志**：记录每次操作的详细信息，便于问题排查

---

## 🎯 功能介绍

### 核心功能模块

#### 1. 用户管理系统 👥
**作用**：管理多个微博账户
**功能**：
- 添加新用户账户（输入用户名和登录信息）
- 删除不需要的用户账户
- 启用或禁用特定用户（暂时停止某个账户的签到）
- 查看所有用户的状态和统计信息
- 更新用户的登录信息（当cookies过期时）

**实际应用场景**：
```
家庭使用：爸爸账户、妈妈账户、孩子账户
工作使用：个人账户、公司账户、备用账户
```

#### 2. 自动签到系统 ✅
**作用**：自动完成超话签到操作
**功能**：
- 单用户签到：为指定用户执行签到
- 多用户批量签到：同时为多个用户执行签到
- 智能重试：签到失败时自动重试
- 结果统计：记录签到成功和失败的数量

**工作原理**：
1. 程序模拟浏览器访问微博网站
2. 使用用户的登录信息（cookies）进行身份验证
3. 自动找到用户关注的超话
4. 逐个点击签到按钮
5. 记录签到结果

#### 3. 超话发现系统 🔍
**作用**：自动发现用户关注的超话
**功能**：
- 扫描用户关注的所有超话
- 自动添加新发现的超话到签到列表
- 过滤已经签到过的超话
- 支持手动添加特定超话

**智能特性**：
- 避免重复添加相同超话
- 自动排除无效或已关闭的超话
- 支持设置最大超话数量限制

#### 4. 定时调度系统 ⏰
**作用**：按时间自动执行签到任务
**功能**：
- 设置每日签到时间（如每天早上9点）
- 支持多个时间点执行
- 自动处理时区问题
- 支持启动和停止调度任务

**使用场景**：
```
早晨签到：设置为9:00，起床后自动完成签到
午休签到：设置为12:00，午休时间自动签到
晚间签到：设置为21:00，睡前自动签到
```

#### 5. 配置管理系统 ⚙️
**作用**：管理程序的各种设置
**功能**：
- 用户个性化设置（每个用户独立配置）
- 签到参数调整（重试次数、延迟时间等）
- 日志记录设置（日志级别、文件大小等）
- 安全设置（是否启用加密存储）

#### 6. 日志记录系统 📝
**作用**：记录程序运行的详细信息
**功能**：
- 每个用户独立的日志文件
- 详细的操作记录（成功、失败、错误原因）
- 自动日志文件轮转（防止文件过大）
- 不同级别的日志信息（调试、信息、警告、错误）

---

## 🏗️ 系统架构图

### 整体架构概览

```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    B --> D[外部服务层]

    A1[命令行界面] --> A
    A2[交互式界面] --> A

    B1[用户管理器] --> B
    B2[签到管理器] --> B
    B3[调度器] --> B
    B4[配置管理器] --> B

    C1[用户数据存储] --> C
    C2[配置文件存储] --> C
    C3[日志文件存储] --> C

    D1[微博API接口] --> D
    D2[加密服务] --> D
```

### 模块关系图

```mermaid
graph LR
    Main[main.py 主程序] --> UM[user_manager.py 用户管理]
    Main --> CM[checkin_manager.py 签到管理]
    Main --> SC[scheduler.py 调度器]
    Main --> CF[config.py 配置管理]

    CM --> WC[weibo_client.py 微博客户端]
    CM --> UM

    UM --> CU[crypto_utils.py 加密工具]

    CF --> CU

    SC --> CM

    WC --> UT[utils.py 工具函数]
```

### 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant M as 主程序
    participant UM as 用户管理器
    participant CM as 签到管理器
    participant WC as 微博客户端
    participant W as 微博服务器

    U->>M: 启动程序
    M->>UM: 加载用户数据
    UM->>M: 返回用户列表
    M->>CM: 执行签到
    CM->>WC: 创建客户端
    WC->>W: 发送签到请求
    W->>WC: 返回签到结果
    WC->>CM: 处理结果
    CM->>M: 返回签到统计
    M->>U: 显示结果
```

---

## 💻 技术栈介绍

### 编程语言：Python 🐍

**为什么选择Python？**
- **简单易学**：语法接近自然语言，容易理解
- **功能强大**：有丰富的第三方库支持
- **跨平台**：可以在Windows、Mac、Linux上运行
- **社区活跃**：遇到问题容易找到解决方案

**Python在本项目中的作用**：
- 处理网络请求（访问微博网站）
- 解析网页内容（提取超话信息）
- 管理文件和数据（保存用户信息和配置）
- 定时任务处理（按时执行签到）

### 核心依赖库详解

#### 1. requests - 网络请求库 🌐
**作用**：负责与微博服务器通信
**功能**：
- 发送HTTP请求（GET、POST等）
- 处理cookies和session
- 自动处理重定向和错误

**通俗解释**：就像浏览器一样，可以访问网页、提交表单、保持登录状态

**代码示例**：
```python
import requests

# 发送GET请求获取网页内容
response = requests.get('https://weibo.com/some_page')
# 发送POST请求提交数据
response = requests.post('https://weibo.com/checkin', data={'topic_id': '123'})
```

#### 2. beautifulsoup4 - 网页解析库 🍲
**作用**：解析HTML网页内容，提取需要的信息
**功能**：
- 解析HTML标签
- 查找特定元素
- 提取文本和属性

**通俗解释**：就像一个智能的"网页阅读器"，可以从复杂的网页中找到我们需要的信息

**代码示例**：
```python
from bs4 import BeautifulSoup

# 解析网页内容
soup = BeautifulSoup(html_content, 'html.parser')
# 查找超话名称
topic_name = soup.find('h1', class_='topic-title').text
```

#### 3. schedule - 定时任务库 ⏰
**作用**：管理定时任务，按时执行签到
**功能**：
- 设置每日、每周、每月的定时任务
- 支持多个时间点
- 自动处理任务调度

**通俗解释**：就像手机的闹钟应用，可以设置在特定时间执行特定任务

**代码示例**：
```python
import schedule
import time

# 设置每天9点执行签到
schedule.every().day.at("09:00").do(run_checkin)

# 持续运行调度器
while True:
    schedule.run_pending()
    time.sleep(1)
```

#### 4. cryptography - 加密库 🔐
**作用**：保护用户敏感信息（如cookies）
**功能**：
- 数据加密和解密
- 密钥生成和管理
- 安全的密码存储

**通俗解释**：就像给重要文件加密码锁，只有知道密码的人才能打开

**代码示例**：
```python
from cryptography.fernet import Fernet

# 生成密钥
key = Fernet.generate_key()
cipher = Fernet(key)

# 加密数据
encrypted_data = cipher.encrypt(b"sensitive_information")
# 解密数据
decrypted_data = cipher.decrypt(encrypted_data)
```

#### 5. fake-useragent - 用户代理库 🎭
**作用**：模拟真实浏览器，避免被网站识别为机器人
**功能**：
- 随机生成浏览器标识
- 模拟不同操作系统和浏览器
- 提高请求成功率

**通俗解释**：就像戴面具一样，让程序看起来像真人在使用浏览器

---

## 📁 代码结构说明

### 项目文件组织

```
weibo_checkin/                 # 项目根目录
├── 📄 main.py                 # 主程序入口
├── 📄 user_manager.py         # 用户管理模块
├── 📄 checkin_manager.py      # 签到管理模块
├── 📄 weibo_client.py         # 微博客户端
├── 📄 scheduler.py            # 定时调度器
├── 📄 config.py               # 配置管理
├── 📄 crypto_utils.py         # 加密工具
├── 📄 utils.py                # 通用工具函数
├── 📄 requirements.txt        # 依赖库列表
├── 📁 logs/                   # 日志文件目录
│   └── 📄 weibo_checkin.log   # 主日志文件
├── 📄 config.json             # 主配置文件
├── 📄 users.json              # 用户数据文件
└── 📁 examples/               # 示例配置文件
    ├── 📄 config.json.example
    └── 📄 users.json.example
```

### 核心模块详解

#### 1. main.py - 程序入口 🚪

**作用**：程序的"大门"，负责接收用户指令并协调各个模块工作

**主要类**：
```python
class WeiboCheckinApp:
    """微博签到应用主类"""

    def __init__(self):
        self.config = Config()                    # 配置管理器
        self.checkin_manager = CheckinManager()   # 签到管理器
        self.scheduler = TaskScheduler()          # 定时调度器
```

**核心功能**：
- **命令行参数处理**：解析用户输入的命令
- **交互式界面**：提供友好的菜单操作
- **模块协调**：调用其他模块完成具体任务
- **错误处理**：捕获和处理各种异常情况

**实际应用场景**：
```bash
# 添加用户
python main.py --add-user user1 "张三" "cookies_data"

# 执行签到
python main.py --multi-checkin

# 启动交互模式
python main.py --interactive
```

#### 2. user_manager.py - 用户管理器 👥

**作用**：管理所有用户账户信息

**主要类**：
```python
class UserManager:
    """用户管理器"""

    def add_user(self, user_id, username, cookies):
        """添加新用户"""

    def remove_user(self, user_id):
        """删除用户"""

    def get_user(self, user_id):
        """获取用户信息"""
```

**数据结构**：
```json
{
  "users": {
    "user1": {
      "user_id": "user1",
      "username": "张三",
      "cookies": {...},
      "enabled_topics": [...],
      "settings": {...},
      "status": {...}
    }
  }
}
```

**核心功能**：
- **用户CRUD操作**：增加、删除、修改、查询用户
- **状态管理**：启用/禁用用户，跟踪签到状态
- **数据持久化**：将用户信息保存到文件
- **安全存储**：支持加密存储敏感信息

#### 3. checkin_manager.py - 签到管理器 ✅

**作用**：负责执行所有签到相关操作

**主要类**：
```python
class CheckinManager:
    """签到管理器"""

    def run_multi_user_checkin(self, user_ids=None, concurrent=False):
        """执行多用户签到"""

    def run_user_checkin(self, user_id):
        """执行单用户签到"""

    def discover_user_topics(self, user_id):
        """发现用户关注的超话"""
```

**工作流程**：
1. **用户验证**：检查用户cookies是否有效
2. **超话发现**：自动发现用户关注的超话
3. **批量签到**：逐个对超话进行签到
4. **结果统计**：记录成功和失败的数量
5. **日志记录**：详细记录每次操作

**并发处理机制**：
```python
# 顺序处理（默认，更安全）
for user_id in user_ids:
    self.run_user_checkin(user_id)
    time.sleep(delay)  # 用户间延迟

# 并发处理（可选，更快速）
with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
    futures = [executor.submit(self.run_user_checkin, user_id)
               for user_id in user_ids]
```

#### 4. weibo_client.py - 微博客户端 🌐

**作用**：与微博服务器进行通信

**主要类**：
```python
class WeiboClient:
    """微博客户端"""

    def __init__(self, cookies):
        """初始化客户端"""

    def get_following_topics(self):
        """获取关注的超话列表"""

    def checkin_topic(self, topic_id):
        """对指定超话进行签到"""
```

**核心技术**：
- **Session管理**：维持登录状态
- **请求头伪装**：模拟真实浏览器
- **错误重试**：网络失败时自动重试
- **反爬虫对策**：随机延迟、User-Agent轮换

**请求流程**：
```python
# 1. 设置请求头
headers = {
    'User-Agent': self.get_random_user_agent(),
    'Referer': 'https://weibo.com/',
    'X-Requested-With': 'XMLHttpRequest'
}

# 2. 发送请求
response = self.session.post(url, data=data, headers=headers)

# 3. 处理响应
if response.status_code == 200:
    result = response.json()
    return self.parse_checkin_result(result)
```

#### 5. crypto_utils.py - 加密工具 🔐

**作用**：保护用户敏感信息

**主要类**：
```python
class CryptoUtils:
    """加密工具类"""

    def encrypt_data(self, data):
        """加密数据"""

    def decrypt_data(self, encrypted_data):
        """解密数据"""
```

**加密原理**：
1. **密钥生成**：使用PBKDF2算法从密码生成密钥
2. **数据加密**：使用AES算法加密用户数据
3. **安全存储**：加密后的数据以Base64格式存储

**安全特性**：
- **强加密算法**：使用AES-256加密
- **密钥派生**：使用PBKDF2防止彩虹表攻击
- **向后兼容**：支持明文和加密两种存储模式

---

## 🧠 核心算法解释

### 1. 超话发现算法

**目标**：自动发现用户关注的所有超话

**算法步骤**：
```python
def discover_topics_algorithm():
    """超话发现算法"""

    # 步骤1：获取用户关注页面
    following_page = get_user_following_page()

    # 步骤2：解析页面，提取超话链接
    topic_links = parse_topic_links(following_page)

    # 步骤3：去重和过滤
    unique_topics = remove_duplicates(topic_links)
    valid_topics = filter_valid_topics(unique_topics)

    # 步骤4：获取详细信息
    topic_details = []
    for topic_link in valid_topics:
        detail = get_topic_detail(topic_link)
        topic_details.append(detail)

    return topic_details
```

**算法优化**：
- **分页处理**：自动处理多页关注列表
- **缓存机制**：避免重复请求相同数据
- **错误恢复**：单个超话失败不影响整体流程

### 2. 并发签到算法

**目标**：安全高效地为多个用户执行签到

**算法设计**：
```python
def concurrent_checkin_algorithm(user_list, max_concurrent=3):
    """并发签到算法"""

    # 步骤1：用户分组
    user_groups = split_users_into_groups(user_list, max_concurrent)

    # 步骤2：并发执行
    for group in user_groups:
        with ThreadPoolExecutor(max_workers=len(group)) as executor:
            # 提交任务
            futures = []
            for user in group:
                future = executor.submit(checkin_single_user, user)
                futures.append((user, future))

            # 收集结果
            for user, future in futures:
                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    record_result(user, result)
                except Exception as e:
                    record_error(user, e)

        # 步骤3：组间延迟
        time.sleep(rate_limit_delay)
```

**安全机制**：
- **速率限制**：控制请求频率，避免触发反机器人
- **超时控制**：防止单个用户阻塞整个流程
- **错误隔离**：单个用户失败不影响其他用户

### 3. 智能重试算法

**目标**：提高签到成功率，处理网络波动

**算法实现**：
```python
def smart_retry_algorithm(operation, max_retries=3):
    """智能重试算法"""

    for attempt in range(max_retries):
        try:
            # 执行操作
            result = operation()

            # 成功则返回
            if result.success:
                return result

            # 分析失败原因
            if result.error_type == 'NETWORK_ERROR':
                delay = calculate_backoff_delay(attempt)
                time.sleep(delay)
                continue
            elif result.error_type == 'AUTH_ERROR':
                # 认证错误，不重试
                break
            else:
                # 其他错误，短暂延迟后重试
                time.sleep(1)
                continue

        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            time.sleep(2 ** attempt)  # 指数退避

    return FailureResult("重试次数已用完")

def calculate_backoff_delay(attempt):
    """计算退避延迟"""
    base_delay = 2
    max_delay = 30
    delay = min(base_delay * (2 ** attempt), max_delay)
    # 添加随机抖动
    jitter = random.uniform(0.1, 0.3) * delay
    return delay + jitter
```

### 4. 数据加密算法

**目标**：安全存储用户敏感信息

**加密流程**：
```python
def encryption_algorithm(data, password):
    """数据加密算法"""

    # 步骤1：密钥派生
    salt = b'weibo_checkin_salt_2024'  # 实际应用中应随机生成
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,  # 10万次迭代
    )
    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))

    # 步骤2：数据序列化
    json_str = json.dumps(data, ensure_ascii=False)
    json_bytes = json_str.encode('utf-8')

    # 步骤3：加密
    fernet = Fernet(key)
    encrypted_data = fernet.encrypt(json_bytes)

    # 步骤4：编码存储
    return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
```

**安全特性**：
- **PBKDF2密钥派生**：防止彩虹表攻击
- **AES-256加密**：业界标准的强加密算法
- **随机IV**：每次加密使用不同的初始向量

#### 1. 用户管理 👥
```
添加用户 → 存储账户信息 → 管理多个账户
```
- **添加用户**：输入用户名和登录信息，系统自动保存
- **删除用户**：不需要的账户可以随时删除
- **启用/禁用**：临时停用某个账户而不删除
- **用户统计**：查看每个用户的签到历史和成功率

#### 2. 自动签到 🤖
```
获取超话列表 → 逐个签到 → 记录结果 → 生成报告
```
- **批量签到**：一次操作为所有用户完成签到
- **智能重试**：签到失败时自动重试
- **结果统计**：详细记录成功、失败、已签到的数量

#### 3. 超话管理 📋
```
自动发现 → 手动添加 → 启用/禁用 → 定期更新
```
- **自动发现**：程序自动找到用户关注的超话
- **手动管理**：可以手动添加或删除特定超话
- **个性化配置**：每个用户可以有不同的超话列表

#### 4. 定时任务 ⏰
```
设置时间 → 自动启动 → 执行签到 → 记录日志
```
- **定时签到**：设置每天固定时间自动签到
- **灵活调度**：可以自定义签到时间和频率

### 高级功能

#### 1. 并发处理 🚀
- **顺序处理**：一个接一个处理用户（更安全）
- **并发处理**：同时处理多个用户（更快速）
- **智能选择**：根据用户数量自动选择最佳方式

#### 2. 安全保护 🔒
- **加密存储**：用户密码和登录信息加密保存
- **访问控制**：防止未授权访问用户数据
- **错误隔离**：一个用户出错不影响其他用户

#### 3. 日志记录 📊
- **详细日志**：记录每次操作的详细信息
- **用户分离**：每个用户有独立的日志文件
- **统计报告**：生成签到成功率等统计信息

---

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────┬─────────────────┬─────────────────────────┤
│   命令行界面     │    交互模式      │      Web界面(未来)      │
│   (CLI)         │  (Interactive)   │     (Future)           │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)                 │
├─────────────────┬─────────────────┬─────────────────────────┤
│   签到管理器     │    用户管理器    │      调度器             │
│ CheckinManager  │  UserManager    │    Scheduler           │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Layer)                     │
├─────────────────┬─────────────────┬─────────────────────────┤
│   配置管理       │    微博客户端    │      加密工具           │
│   Config        │  WeiboClient    │    CryptoUtils         │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   存储层 (Storage Layer)                     │
├─────────────────┬─────────────────┬─────────────────────────┤
│   配置文件       │    用户数据      │      日志文件           │
│  config.json    │   users.json    │     *.log              │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 模块关系说明

#### 1. 用户界面层
这是用户直接接触的部分，就像手机App的界面：
- **命令行界面**：通过输入命令来操作程序
- **交互模式**：像聊天一样，程序问什么您答什么
- **Web界面**：未来可能添加的网页版界面

#### 2. 业务逻辑层
这是程序的"大脑"，负责处理各种业务：
- **签到管理器**：专门负责签到相关的所有操作
- **用户管理器**：管理用户账户的增删改查
- **调度器**：负责定时任务的执行

#### 3. 数据访问层
这是程序与外部系统交互的部分：
- **配置管理**：读取和保存程序设置
- **微博客户端**：与微博网站进行通信
- **加密工具**：保护用户数据安全

#### 4. 存储层
这是数据保存的地方：
- **配置文件**：程序的各种设置
- **用户数据**：用户账户和超话信息
- **日志文件**：操作记录和错误信息

---

## 💻 技术栈介绍

### 编程语言：Python 🐍

**为什么选择Python？**
- **简单易学**：语法接近自然语言，容易理解
- **功能强大**：有丰富的第三方库支持
- **跨平台**：Windows、Mac、Linux都能运行
- **社区活跃**：遇到问题容易找到解决方案

### 核心依赖库

#### 1. requests - 网络请求库
```python
import requests
response = requests.get('https://weibo.com')
```
**作用**：就像浏览器一样，向微博网站发送请求获取数据
**通俗解释**：程序需要"访问"微博网站，requests就是它的"浏览器"

#### 2. beautifulsoup4 - 网页解析库
```python
from bs4 import BeautifulSoup
soup = BeautifulSoup(html_content, 'html.parser')
```
**作用**：解析网页内容，提取需要的信息
**通俗解释**：网页就像一本书，BeautifulSoup帮助程序"阅读"这本书并找到需要的内容

#### 3. cryptography - 加密库
```python
from cryptography.fernet import Fernet
encrypted_data = fernet.encrypt(data)
```
**作用**：加密用户的敏感信息
**通俗解释**：就像给重要文件加密码锁，保护用户隐私

#### 4. schedule - 定时任务库
```python
import schedule
schedule.every().day.at("09:00").do(job)
```
**作用**：设置定时任务
**通俗解释**：就像闹钟一样，到了设定时间就自动执行任务

### 开发工具和环境

#### Python版本要求
- **最低版本**：Python 3.7+
- **推荐版本**：Python 3.9+
- **原因**：新版本有更好的性能和安全性

#### 开发环境
- **代码编辑器**：VS Code、PyCharm等
- **版本控制**：Git
- **包管理**：pip

---

## 📁 代码结构

### 项目文件组织

```
weibo-checkin/                 # 项目根目录
├── main.py                    # 主程序入口
├── config.py                  # 配置管理模块
├── user_manager.py            # 用户管理模块
├── checkin_manager.py         # 签到管理模块
├── weibo_client.py           # 微博客户端模块
├── crypto_utils.py           # 加密工具模块
├── scheduler.py              # 定时任务模块
├── utils.py                  # 工具函数模块
├── requirements.txt          # 依赖库列表
├── config.json               # 主配置文件
├── users.json               # 用户数据文件
├── logs/                    # 日志目录
│   ├── weibo_checkin.log    # 主日志文件
│   └── weibo_checkin_user_*.log  # 用户日志文件
├── docs/                    # 文档目录
│   ├── MULTI_USER_GUIDE.md  # 使用指南
│   └── TECHNICAL_DOCUMENTATION.md  # 技术文档
└── examples/                # 示例文件
    ├── config_multi_user.json.example
    └── users.json.example
```

### 核心模块详解

#### 1. main.py - 主程序 🚪
```python
class WeiboCheckinApp:
    def __init__(self):
        self.config = Config()
        self.checkin_manager = CheckinManager(self.config)
        # ...
```
**作用**：程序的入口，就像房子的大门
**功能**：
- 处理用户输入的命令
- 协调各个模块的工作
- 提供用户界面

#### 2. user_manager.py - 用户管理器 👥
```python
class UserManager:
    def add_user(self, user_id, username, cookies):
        # 添加新用户
    def remove_user(self, user_id):
        # 删除用户
```
**作用**：管理所有用户账户
**功能**：
- 添加、删除、修改用户信息
- 用户状态管理（启用/禁用）
- 用户统计信息

#### 3. checkin_manager.py - 签到管理器 ✅
```python
class CheckinManager:
    def run_multi_user_checkin(self, user_ids):
        # 执行多用户签到
    def run_user_checkin(self, user_id):
        # 执行单用户签到
```
**作用**：负责所有签到相关操作
**功能**：
- 单用户签到
- 多用户批量签到
- 并发处理控制
- 结果统计和报告
