"""
配置管理模块
支持多用户配置管理
"""
import json
import os
from typing import Dict, List, Optional
from user_manager import UserManager


class Config:
    """配置管理类 - 支持多用户"""

    def __init__(self, config_file: str = "config.json", users_file: str = "users.json"):
        self.config_file = config_file
        self.users_file = users_file
        self.config = self._load_config()
        self.user_manager = UserManager(users_file)
        self.current_user_id = None  # 当前操作的用户ID
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                print(f"⚠️  配置文件 {self.config_file} 格式错误，使用默认配置")
        else:
            # 配置文件不存在，尝试自动创建
            print(f"📝 配置文件 {self.config_file} 不存在，正在创建默认配置...")
            default_config = self._get_default_config()

            # 尝试从示例文件创建
            if self._create_config_from_example():
                print("✅ 已从示例文件创建配置文件")
                try:
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except:
                    pass

            # 如果示例文件不存在或创建失败，使用默认配置并保存
            print("📝 使用默认配置并保存到文件")
            self._save_default_config(default_config)
            return default_config

        # 返回默认配置（保持向后兼容）
        return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "multi_user": {
                "enabled": True,
                "default_user": None,
                "concurrent_processing": False,
                "max_concurrent_users": 3,
                "rate_limit_delay": 5
            },
            "user": {
                "cookies": {}
            },
            "checkin": {
                "enabled_topics": [],
                "auto_discover": True,
                "max_topics": 50,
                "retry_times": 3,
                "delay_between_checkins": 2
            },
            "schedule": {
                "enabled": False,
                "time": "09:00",
                "timezone": "Asia/Shanghai"
            },
            "logging": {
                "level": "INFO",
                "file": "logs/weibo_checkin.log",
                "max_size": "10MB",
                "backup_count": 5,
                "separate_user_logs": True
            }
        }

    def _create_config_from_example(self) -> bool:
        """从示例文件创建配置文件"""
        import shutil

        # 优先使用多用户示例文件
        example_files = [
            "config_multi_user.json.example",
            "config.json.example"
        ]

        for example_file in example_files:
            if os.path.exists(example_file):
                try:
                    shutil.copy(example_file, self.config_file)
                    print(f"📋 已从 {example_file} 创建配置文件")
                    return True
                except Exception as e:
                    print(f"⚠️  从 {example_file} 创建配置失败: {e}")

        return False

    def _save_default_config(self, config: Dict) -> None:
        """保存默认配置到文件"""
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir:
                os.makedirs(config_dir, exist_ok=True)

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 默认配置已保存到 {self.config_file}")
        except Exception as e:
            print(f"❌ 保存默认配置失败: {e}")
    
    def save_config(self) -> None:
        """保存配置到文件"""
        config_dir = os.path.dirname(self.config_file)
        if config_dir:  # 只有当目录不为空时才创建
            os.makedirs(config_dir, exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    def get(self, key: str, default=None):
        """获取配置项"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value) -> None:
        """设置配置项"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
    
    def get_user_config(self) -> Dict:
        """获取用户配置"""
        return self.config.get("user", {})
    
    def get_checkin_config(self) -> Dict:
        """获取签到配置"""
        return self.config.get("checkin", {})
    
    def get_schedule_config(self) -> Dict:
        """获取调度配置"""
        return self.config.get("schedule", {})
    
    def get_logging_config(self) -> Dict:
        """获取日志配置"""
        return self.config.get("logging", {})
    
    def update_cookies(self, cookies: Dict) -> None:
        """更新cookies"""
        self.set("user.cookies", cookies)
        self.save_config()
    
    def add_topic(self, topic_id: str, topic_name: str) -> None:
        """添加超话"""
        topics = self.get("checkin.enabled_topics", [])
        topic_info = {"id": topic_id, "name": topic_name, "enabled": True}
        
        # 检查是否已存在
        for i, topic in enumerate(topics):
            if topic.get("id") == topic_id:
                topics[i] = topic_info
                break
        else:
            topics.append(topic_info)
        
        self.set("checkin.enabled_topics", topics)
        self.save_config()
    
    def remove_topic(self, topic_id: str) -> None:
        """移除超话"""
        topics = self.get("checkin.enabled_topics", [])
        topics = [t for t in topics if t.get("id") != topic_id]
        self.set("checkin.enabled_topics", topics)
        self.save_config()
    
    def get_enabled_topics(self) -> List[Dict]:
        """获取启用的超话列表"""
        # 如果设置了当前用户，返回该用户的超话列表
        if self.current_user_id:
            user_data = self.user_manager.get_user(self.current_user_id)
            if user_data:
                topics = user_data.get('enabled_topics', [])
                return [t for t in topics if t.get("enabled", True)]

        # 否则返回传统配置中的超话列表（向后兼容）
        topics = self.get("checkin.enabled_topics", [])
        return [t for t in topics if t.get("enabled", True)]

    # ========== 多用户管理方法 ==========

    def set_current_user(self, user_id: str) -> bool:
        """
        设置当前操作的用户

        Args:
            user_id: 用户ID

        Returns:
            True如果设置成功，否则False
        """
        if self.user_manager.get_user(user_id):
            self.current_user_id = user_id
            return True
        return False

    def get_current_user(self) -> Optional[str]:
        """获取当前用户ID"""
        return self.current_user_id

    def is_multi_user_enabled(self) -> bool:
        """检查是否启用多用户模式"""
        return self.get("multi_user.enabled", True)

    def add_user(self, user_id: str, username: str, cookies: Dict,
                 enabled_topics: List[Dict] = None, settings: Dict = None) -> bool:
        """
        添加用户

        Args:
            user_id: 用户ID
            username: 用户名
            cookies: 用户cookies
            enabled_topics: 启用的超话列表
            settings: 用户设置

        Returns:
            True如果添加成功，否则False
        """
        return self.user_manager.add_user(user_id, username, cookies, enabled_topics, settings)

    def remove_user(self, user_id: str) -> bool:
        """移除用户"""
        return self.user_manager.remove_user(user_id)

    def get_user(self, user_id: str) -> Optional[Dict]:
        """获取用户信息"""
        return self.user_manager.get_user(user_id)

    def get_all_users(self) -> Dict[str, Dict]:
        """获取所有用户信息"""
        return self.user_manager.get_all_users()

    def get_enabled_users(self) -> Dict[str, Dict]:
        """获取所有启用的用户"""
        return self.user_manager.get_enabled_users()

    def list_users(self) -> List[Dict]:
        """列出所有用户的基本信息"""
        return self.user_manager.list_users()

    def update_user_cookies(self, user_id: str, cookies: Dict) -> bool:
        """更新用户cookies"""
        return self.user_manager.update_user_cookies(user_id, cookies)

    def add_user_topic(self, user_id: str, topic_id: str, topic_name: str) -> bool:
        """为用户添加超话"""
        return self.user_manager.add_user_topic(user_id, topic_id, topic_name)

    def remove_user_topic(self, user_id: str, topic_id: str) -> bool:
        """移除用户超话"""
        return self.user_manager.remove_user_topic(user_id, topic_id)

    def enable_user(self, user_id: str) -> bool:
        """启用用户"""
        return self.user_manager.enable_user(user_id)

    def disable_user(self, user_id: str) -> bool:
        """禁用用户"""
        return self.user_manager.disable_user(user_id)

    def get_user_statistics(self, user_id: str) -> Optional[Dict]:
        """获取用户统计信息"""
        return self.user_manager.get_user_statistics(user_id)

    def update_user_status(self, user_id: str, checkin_result: Dict) -> bool:
        """更新用户签到状态"""
        return self.user_manager.update_user_status(user_id, checkin_result)
